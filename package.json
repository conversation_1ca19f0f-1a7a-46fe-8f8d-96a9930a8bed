{"name": "junkchk", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.9.26", "@types/react": "~19.0.10", "expo": "~53.0.9", "expo-application": "^6.1.4", "expo-camera": "^16.1.6", "expo-device": "^7.1.4", "expo-file-system": "~18.1.10", "expo-haptics": "^14.1.4", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-navigation-bar": "~4.2.4", "expo-secure-store": "^14.2.3", "expo-splash-screen": "^0.30.8", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "react": "19.0.0", "react-content-loader": "^7.0.0", "react-native": "0.79.2", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.24.0", "react-native-markdown-display": "^7.0.2", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "react-native-webview": "^13.13.5", "typescript": "~5.8.3"}, "devDependencies": {"@babel/core": "^7.24.0", "@react-native-community/cli": "^13.5.1", "@types/react-native": "^0.73.0", "babel-plugin-module-resolver": "^5.0.2"}, "private": true}