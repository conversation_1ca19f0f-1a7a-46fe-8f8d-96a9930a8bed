import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Platform,
  UIManager,
  BackHandler
} from 'react-native';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';

import { Colors, Spacing } from '../theme';
import Screen from '../Components/Screen';
import Typography from '../Components/Typography';
import GlowingGradeIndicator from '../Components/GlowingGradeIndicator';
import GlowingButton from '../Components/GlowingButton';
import InfoCards from '../Components/InfoCards';
import {
  Ionicons,
  MaterialCommunityIcons,
  FontAwesome5
} from '@expo/vector-icons';
import { useScan, useHistory, useAuth, ScanResult, Grade } from '../context';

// Enable LayoutAnimation for Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

// Define types for the JSON structure
interface AllergyWarning {
  ingredient: string;
  message: string;
}

interface Warning {
  type: string;
  message: string;
}





// Screen dimensions can be used for responsive design if needed

// Generate a unique ID for scan results
const generateUniqueId = () => {
  return `scan-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
};

const ScanResultsScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { scanState, clearCurrentScan, completeScan } = useScan();
  const { historyState, addScanToHistory } = useHistory();
  const { authState } = useAuth();

  // Ref to prevent multiple processing of the same API response
  const hasProcessedResponse = useRef(false);

  // Extract params from route
  const params = route.params as any || {};
  const imageUri = params.imageUri || '';
  const apiResponse = params.apiResponse;

  // Determine the source of navigation
  const isFromHistory = params.fromHistory === true;

  // Always use the current authentication state to determine if user is anonymous
  // This ensures the button shown is always correct based on current auth state
  const isAnonymous = !authState.isAuthenticated;

  // Handle back navigation - go to Home or History based on where the user came from
  const handleBackNavigation = async () => {
    // Navigate based on where the user came from
    // Note: Scan saving is handled in the useEffect hook, no need to duplicate here
    if (isFromHistory) {
      // If coming from history, go back to history
      navigation.navigate('History' as never);
    } else {
      // Otherwise, go back to home
      navigation.navigate('Home' as never);
    }
  };

  // Prevent hardware back button on Android from going back to scan screen
  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        // Use the same navigation logic as the button
        handleBackNavigation();
        return true;
      };

      // Add event listener
      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);

      // Clean up event listener on unmount
      return () => subscription.remove();
    }, [handleBackNavigation])
  );

  // Reduced logging for production
  // console.log(`ScanResultsScreen: isAuthenticated=${authState.isAuthenticated}, isAnonymous=${isAnonymous}, fromHistory=${isFromHistory}`);

  // Use current scan from context if available
  const currentScan = scanState.currentScan;

  // Error handling for missing API response
  if (!apiResponse && !scanState.currentScan) {
    return (
      <Screen style={styles.container}>
        <View style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          padding: 20
        }}>
          <Typography variant="heading2" style={{
            marginBottom: 20,
            textAlign: 'center',
            color: Colors.Error
          }}>
            Error: No product data available
          </Typography>
          <GlowingButton
            title="Try Again"
            onPress={() => navigation.navigate('Scan' as never)}
            variant="primary"
            style={{
              width: '100%',
              marginTop: 20
            }}
          />
        </View>
      </Screen>
    );
  }

  // Use either the API response or the current scan from context
  let productName, grade, summary, goodIngredients, badIngredients, allergyWarnings, warnings, explanationDetails, effects, infoCards;

  if (!isFromHistory && apiResponse) {
    // For new scans, prioritize the API response
    productName = apiResponse?.product_details?.name || 'Unknown Product';
    grade = (apiResponse?.rating || 'C') as Grade;
    summary = apiResponse?.summary || apiResponse?.detailed_rationale?.overall_assessment || 'No summary available';

    // Use the enhanced good_ingredients from API response with proper descriptions
    goodIngredients = apiResponse?.good_ingredients?.map((ingredient: any) => ({
      name: ingredient.name || 'Unknown Ingredient',
      description: ingredient.description || 'Beneficial ingredient with positive effects'
    })) || [];

    // Use the enhanced harmful_ingredient_analysis with proper descriptions
    badIngredients = apiResponse?.harmful_ingredient_analysis
      ?.map((analysis: any) => ({
        name: analysis.ingredient,
        description: analysis.impact || 'Potential concern - consult product information'
      })) || [];
    allergyWarnings = [];
    warnings = apiResponse?.explanation?.influencing_ingredients
      ?.map((ingredient: string) => ({
        type: 'ingredient',
        message: `Contains ${ingredient}`
      })) || [];
    explanationDetails = apiResponse?.explanation?.rationale || '';

    // Extract effects from API response
    effects = apiResponse?.effects || {
      positive_effects: { short_term: [], long_term: [] },
      negative_effects: { short_term: [], long_term: [] }
    };
  } else if (scanState.currentScan) {
    // For history items or if no API response, use the current scan from context
    const scan = scanState.currentScan;
    productName = scan.productName;
    grade = scan.grade;
    summary = scan.summary;
    goodIngredients = scan.goodIngredients;
    badIngredients = scan.badIngredients;
    allergyWarnings = scan.allergyWarnings;
    warnings = scan.warnings;
    explanationDetails = scan.explanation;
    effects = scan.effects || {
      positive_effects: { short_term: [], long_term: [] },
      negative_effects: { short_term: [], long_term: [] }
    };
    infoCards = scan.infoCards;
  } else {
    // Fallback to API response if no current scan
    productName = apiResponse?.product_details?.name || 'Unknown Product';
    grade = (apiResponse?.rating || 'C') as Grade;
    summary = apiResponse?.summary || apiResponse?.detailed_rationale?.overall_assessment || 'No summary available';

    // Use the enhanced good_ingredients from API response with proper descriptions
    goodIngredients = apiResponse?.good_ingredients?.map((ingredient: any) => ({
      name: ingredient.name || 'Unknown Ingredient',
      description: ingredient.description || 'Beneficial ingredient with positive effects'
    })) || [];

    // Use the enhanced harmful_ingredient_analysis with proper descriptions
    badIngredients = apiResponse?.harmful_ingredient_analysis
      ?.map((analysis: any) => ({
        name: analysis.ingredient,
        description: analysis.impact || 'Potential concern - consult product information'
      })) || [];

    // Map allergy warnings (if any)
    allergyWarnings = [];

    // Map general warnings
    warnings = apiResponse?.explanation?.influencing_ingredients
      ?.map((ingredient: string) => ({
        type: 'ingredient',
        message: `Contains ${ingredient}`
      })) || [];

    // Detailed explanation
    explanationDetails = apiResponse?.explanation?.rationale || '';

    // Extract effects from API response
    effects = apiResponse?.effects || {
      positive_effects: { short_term: [], long_term: [] },
      negative_effects: { short_term: [], long_term: [] }
    };

    // Extract infoCards from API response
    infoCards = apiResponse?.info_cards;
  }

  // Create a productData object to match the existing UI
  const ingredientBreakdown = {
    goodIngredients,
    badIngredients
  };

  // State for showing detailed explanation and tracking selected ingredient tab
  const [showDetailedExplanation, setShowDetailedExplanation] = useState(false);
  const [selectedTab, setSelectedTab] = useState('good');

  // Get grade color with optional opacity
  const getGradeColor = useCallback((opacity = 1) => {
    let color: string;
    switch (grade) {
      case 'A': color = Colors.GradeA; break;
      case 'B': color = Colors.GradeB; break;
      case 'C': color = Colors.GradeC; break;
      case 'D': color = Colors.GradeD; break;
      case 'E': color = Colors.GradeE; break;
      default: color = Colors.GradeA;
    }

    // If opacity is 1, return the original color
    if (opacity === 1) return color;

    // Otherwise, convert to rgba
    const hexToRgb = (hex: string) => {
      const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
      const formattedHex = hex.replace(shorthandRegex, (_m, r, g, b) => r + r + g + g + b + b);
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(formattedHex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : { r: 0, g: 0, b: 0 };
    };

    const rgb = hexToRgb(color);
    return `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${opacity})`;
  }, [grade]);

  // Generate a unique ID for scan results
  const generateUniqueId = () => {
    return `scan-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  };

  // Reset the processed flag when the component mounts with new data
  useEffect(() => {
    hasProcessedResponse.current = false;
  }, [imageUri, apiResponse?.product_details?.name]);

  // Create a scan result object from the API response
  useEffect(() => {
    if (apiResponse && !isFromHistory && !hasProcessedResponse.current) {
      // Mark as processed to prevent multiple executions
      hasProcessedResponse.current = true;

      // Create the scan result object
      const scanResult: ScanResult = {
        id: generateUniqueId(),
        imageUri,
        timestamp: new Date().toISOString(),
        productName: apiResponse?.product_details?.name || 'Unknown Product',
        grade: (apiResponse?.rating || 'C') as Grade,
        summary: apiResponse?.summary || apiResponse?.detailed_rationale?.overall_assessment || 'No summary available',
        goodIngredients: apiResponse?.good_ingredients?.map((ingredient: any) => ({
          name: ingredient.name || 'Unknown Ingredient',
          description: ingredient.description || 'Beneficial ingredient with positive effects'
        })) || [],
        badIngredients: apiResponse?.harmful_ingredient_analysis
          ?.map((analysis: any) => ({
            name: analysis.ingredient,
            description: analysis.impact || 'Potential concern - consult product information'
          })) || [],
        allergyWarnings: [],
        warnings: apiResponse?.explanation?.influencing_ingredients
          ?.map((ingredient: string) => ({
            type: 'ingredient',
            message: `Contains ${ingredient}`
          })) || [],
        explanation: apiResponse?.explanation?.rationale || '',
        effects: apiResponse?.effects || {
          positive_effects: { short_term: [], long_term: [] },
          negative_effects: { short_term: [], long_term: [] }
        },
        infoCards: apiResponse?.info_cards || undefined,
      };

      // Always save to scan context first
      completeScan(scanResult);

      // Note: History saving is handled by the authentication state change useEffect
    }
  }, [apiResponse, isFromHistory, completeScan, addScanToHistory, imageUri]);

  // Handle authentication state changes - save current scan to history when user becomes authenticated
  useEffect(() => {
    if (!isAnonymous && scanState.currentScan && !isFromHistory) {
      // User just became authenticated and we have a current scan that's not from history
      // Save it to history if it's not already there
      const existingInHistory = historyState.scans.find(
        scan => scan.productName.toLowerCase() === scanState.currentScan!.productName.toLowerCase()
      );

      if (!existingInHistory) {
        addScanToHistory(scanState.currentScan);
      }
    }
  }, [isAnonymous, scanState.currentScan, historyState.scans, addScanToHistory, isFromHistory]);

  const handleSaveAndUnlock = async () => {
    if (isAnonymous) {
      // If not authenticated, navigate to login with current scan info
      navigation.navigate('PhoneNumberLogin' as never, {
        imageUri,
        returnToScan: true,
        scanData: {
          imageUri,
          apiResponse
        }
      } as never);
    } else {
      // For authenticated users, the scan should already be saved to history via the useEffect
      // Just ensure we have a current scan in context
      if (scanState.currentScan) {
        // Scan already saved, no action needed
      } else if (apiResponse) {
        // Fallback: if somehow no current scan exists, create and save one
        const scanResult: ScanResult = {
          id: generateUniqueId(),
          imageUri,
          timestamp: new Date().toISOString(),
          productName: productName,
          grade: grade,
          summary: summary,
          goodIngredients: goodIngredients,
          badIngredients: badIngredients,
          allergyWarnings: allergyWarnings,
          warnings: warnings,
          explanation: explanationDetails,
          effects: effects,
          infoCards: infoCards,
        };

        await completeScan(scanResult);
        await addScanToHistory(scanResult);
      }

      // Navigate based on where the user came from
      if (isFromHistory) {
        navigation.navigate('History' as never);
      } else {
        navigation.navigate('Home' as never);
      }
    }
  };





  return (
    <>
      <Screen
        scrollable
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
        safeArea={true}
      >


        {/* Centered Grade Indicator */}
        <View style={styles.gradeContainer}>
          <View style={styles.gradeBadgeContainer}>
            <GlowingGradeIndicator grade={grade} size="large" style={styles.gradeIndicator} />
            <View style={styles.gradeShadow} />
          </View>
        </View>

        {/* Product Name */}
        <Typography
          variant="heading1"
          style={styles.productName}
          numberOfLines={2}
        >
          {productName}
        </Typography>

        {/* Anonymous Indicator */}
        {isAnonymous && (
          <View style={styles.anonymousContainer}>
            <Ionicons name="information-circle" size={16} color={Colors.DarkText} />
            <Typography
              variant="description"
              style={styles.anonymousLabel}
            >
              This scan is not saved
            </Typography>
          </View>
        )}

        {/* Information Cards Section */}
        <InfoCards
          apiResponse={apiResponse}
          grade={grade}
          goodIngredients={goodIngredients}
          badIngredients={badIngredients}
          effects={effects}
          productName={productName}
          savedInfoCards={scanState.currentScan?.infoCards}
        />

        {/* Divider */}
        <View style={styles.divider} />



        {/* Ingredient Breakdown Section - Most Important */}
        <View style={[styles.section, styles.importantSection]}>
          <View style={styles.sectionHeader}>
            <View style={[styles.iconContainer, { backgroundColor: 'rgba(52, 199, 89, 0.1)' }]}>
              <MaterialCommunityIcons name="flask-outline" size={18} color={Colors.Success} />
            </View>
            <Typography variant="heading2" style={styles.sectionTitle}>
              Ingredient Breakdown
            </Typography>
          </View>

          {/* Ingredient tabs */}
          <View style={styles.ingredientTabs}>
            <TouchableOpacity
              style={[
                styles.ingredientTab,
                { borderBottomColor: selectedTab === 'good' ? Colors.Success : 'transparent' }
              ]}
              activeOpacity={0.7}
              onPress={() => setSelectedTab('good')}
            >
              <View style={styles.tabContent}>
                <Ionicons
                  name="checkmark-circle"
                  size={16}
                  color={selectedTab === 'good' ? Colors.Success : Colors.LightText}
                />
                <Typography
                  variant="bodyText"
                  style={styles.tabText}
                  color={selectedTab === 'good' ? Colors.Success : Colors.LightText}
                >
                  Good ({ingredientBreakdown.goodIngredients.length})
                </Typography>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.ingredientTab,
                { borderBottomColor: selectedTab === 'concerning' ? Colors.Error : 'transparent' }
              ]}
              activeOpacity={0.7}
              onPress={() => setSelectedTab('concerning')}
            >
              <View style={styles.tabContent}>
                <Ionicons
                  name="close-circle"
                  size={16}
                  color={selectedTab === 'concerning' ? Colors.Error : Colors.LightText}
                />
                <Typography
                  variant="bodyText"
                  style={styles.tabText}
                  color={selectedTab === 'concerning' ? Colors.Error : Colors.LightText}
                >
                  Concerning ({ingredientBreakdown.badIngredients.length})
                </Typography>
              </View>
            </TouchableOpacity>
          </View>

          {/* Ingredients List - Conditionally render based on selected tab */}
          {selectedTab === 'good' ? (
            <View style={styles.ingredientList}>
              {ingredientBreakdown.goodIngredients.map((ingredient: { name: string, description: string }, index: number) => (
                <View key={`good-${index}`} style={styles.ingredientItem}>
                  <View style={[styles.ingredientIcon, { backgroundColor: 'rgba(52, 199, 89, 0.1)' }]}>
                    <Ionicons name="leaf-outline" size={16} color={Colors.Success} />
                  </View>
                  <View style={styles.ingredientContent}>
                    <Typography variant="bodyText" style={styles.ingredientName}>
                      {ingredient.name}
                    </Typography>
                    <Typography variant="description" style={styles.ingredientDescription}>
                      {ingredient.description}
                    </Typography>
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <View style={styles.ingredientList}>
              {ingredientBreakdown.badIngredients.map((ingredient: { name: string, description: string }, index: number) => (
                <View key={`bad-${index}`} style={styles.ingredientItem}>
                  <View style={[styles.ingredientIcon, { backgroundColor: 'rgba(255, 59, 48, 0.1)' }]}>
                    <Ionicons name="warning-outline" size={16} color={Colors.Error} />
                  </View>
                  <View style={styles.ingredientContent}>
                    <Typography variant="bodyText" style={styles.ingredientName}>
                      {ingredient.name}
                    </Typography>
                    <Typography variant="description" style={styles.ingredientDescription}>
                      {ingredient.description}
                    </Typography>
                  </View>
                </View>
              ))}
            </View>
          )}
        </View>

        {/* Divider */}
        <View style={styles.divider} />

        {/* Short-term Effects Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <View style={[styles.iconContainer, { backgroundColor: 'rgba(90, 200, 250, 0.1)' }]}>
              <Ionicons name="time-outline" size={18} color={Colors.AccentBlue} />
            </View>
            <Typography variant="heading2" style={styles.sectionTitle}>
              Short-term Effects
            </Typography>
          </View>

          <View style={styles.effectsList}>
            {/* Display actual short-term effects from API response */}
            {(() => {
              // Combine positive and negative short-term effects
              const positiveShortTerm = effects?.positive_effects?.short_term || [];
              const negativeShortTerm = effects?.negative_effects?.short_term || [];
              const allShortTermEffects = [...positiveShortTerm, ...negativeShortTerm];

              // If no effects available, show fallback message
              if (allShortTermEffects.length === 0) {
                return (
                  <View style={styles.effectItem}>
                    <View style={styles.effectBullet} />
                    <Typography variant="bodyText" style={styles.effectText}>
                      No specific short-term effects identified for this product.
                    </Typography>
                  </View>
                );
              }

              return allShortTermEffects.map((effect, index) => (
                <View key={`short-${index}`} style={styles.effectItem}>
                  <View style={styles.effectBullet} />
                  <Typography variant="bodyText" style={styles.effectText}>
                    {effect}
                  </Typography>
                </View>
              ));
            })()}
          </View>
        </View>

        {/* Divider */}
        <View style={styles.divider} />

        {/* Long-term Effects Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <View style={[styles.iconContainer, { backgroundColor: 'rgba(255, 149, 0, 0.1)' }]}>
              <Ionicons name="calendar-outline" size={18} color={Colors.Warning} />
            </View>
            <Typography variant="heading2" style={styles.sectionTitle}>
              Long-term Effects
            </Typography>
          </View>

          <View style={styles.effectsList}>
            {/* Display actual long-term effects from API response */}
            {(() => {
              // Combine positive and negative long-term effects
              const positiveLongTerm = effects?.positive_effects?.long_term || [];
              const negativeLongTerm = effects?.negative_effects?.long_term || [];
              const allLongTermEffects = [...positiveLongTerm, ...negativeLongTerm];

              // If no effects available, show fallback message
              if (allLongTermEffects.length === 0) {
                return (
                  <View style={styles.effectItem}>
                    <View style={styles.effectBullet} />
                    <Typography variant="bodyText" style={styles.effectText}>
                      No specific long-term effects identified for this product.
                    </Typography>
                  </View>
                );
              }

              return allLongTermEffects.map((effect, index) => (
                <View key={`long-${index}`} style={styles.effectItem}>
                  <View style={styles.effectBullet} />
                  <Typography variant="bodyText" style={styles.effectText}>
                    {effect}
                  </Typography>
                </View>
              ));
            })()}
          </View>
        </View>
      </Screen>

      {/* Fixed Button at bottom */}
      <View style={styles.fixedButtonContainer}>
        {isAnonymous ? (
          // Show Save & Unlock button for anonymous users
          <GlowingButton
            title="Save & Unlock Features"
            onPress={handleSaveAndUnlock}
            variant="primary"
            style={styles.fixedButton}
            glowIntensity={0.8}
            alwaysGlow={true}
            icon={
              <View style={styles.iconWrapper}>
                <Ionicons name="save" size={24} color={Colors.BackgroundPrimary} />
              </View>
            }
          />
        ) : (
          // Show Back button for authenticated users with dynamic text based on destination
          <GlowingButton
            title={isFromHistory ? "Back to History" : "Back to Home"}
            onPress={handleBackNavigation}
            variant="primary"
            style={styles.fixedButton}
            glowIntensity={0.8}
            alwaysGlow={true}
            icon={
              <View style={styles.iconWrapper}>
                <Ionicons name="arrow-back" size={24} color={Colors.BackgroundPrimary} />
              </View>
            }
          />
        )}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary, // White background
  },
  contentContainer: {
    paddingBottom: Platform.OS === 'ios' ? 120 : 100, // Extra padding to account for fixed button
    paddingHorizontal: Spacing.Large,
  },



  // Grade indicator
  gradeContainer: {
    alignItems: 'center',
    marginTop: Spacing.XXLarge,
    marginBottom: Spacing.Large,
  },
  gradeBadgeContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    width: 120,
    height: 120,
  },
  gradeIndicator: {
    width: 120,
    height: 120,
    zIndex: 2,
  },
  gradeShadow: {
    position: 'absolute',
    width: 116,
    height: 116,
    borderRadius: 58,
    backgroundColor: 'rgba(0,0,0,0.2)',
    bottom: -4,
    right: -4,
    zIndex: 1,
  },

  // Product name
  productName: {
    textAlign: 'center',
    fontWeight: 'bold',
    marginBottom: Spacing.Medium,
    color: Colors.DarkText,
  },

  // Anonymous indicator
  anonymousContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
    paddingVertical: Spacing.ExtraSmall,
    paddingHorizontal: Spacing.Small,
    borderRadius: 8, // Small border radius
    marginBottom: Spacing.Large,
    alignSelf: 'center',
  },
  anonymousLabel: {
    fontStyle: 'italic',
    marginLeft: Spacing.ExtraSmall,
    color: Colors.DarkText,
    fontWeight: '500',
  },

  // Section styles
  section: {
    marginBottom: Spacing.Large,
  },
  importantSection: {
    backgroundColor: 'rgba(240, 240, 240, 0.5)',
    borderRadius: 12,
    padding: Spacing.Medium,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.Medium,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.Small,
  },
  sectionTitle: {
    marginLeft: Spacing.Small,
    fontWeight: '600',
  },

  // Divider
  divider: {
    height: 1,
    backgroundColor: Colors.SurfaceSecondary,
    marginVertical: Spacing.Medium,
  },

  // Summary section
  summaryText: {
    lineHeight: 22,
    marginBottom: Spacing.Medium,
  },
  gradeSummary: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.Small,
    borderTopWidth: 1,
    borderTopColor: Colors.SurfaceSecondary,
  },
  gradeDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: Spacing.Small,
  },
  gradeSummaryText: {
    fontWeight: '500',
  },
  detailsToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingVertical: Spacing.Small,
  },
  detailsToggleText: {
    marginRight: Spacing.ExtraSmall,
  },
  detailedExplanation: {
    paddingTop: Spacing.Small,
    paddingBottom: Spacing.Small,
    borderTopWidth: 1,
    borderTopColor: Colors.SurfaceSecondary,
    lineHeight: 20,
  },
  gradeExplanation: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    paddingTop: Spacing.Small,
    borderTopWidth: 1,
    borderTopColor: Colors.SurfaceSecondary,
  },
  gradeExplanationText: {
    marginRight: Spacing.ExtraSmall,
  },
  inlineGrade: {
    marginHorizontal: Spacing.ExtraSmall,
  },

  // Warning section
  warningSection: {
    marginBottom: Spacing.Medium,
    paddingBottom: Spacing.Small,
    borderBottomWidth: 1,
    borderBottomColor: Colors.SurfaceSecondary,
  },
  lastSection: {
    borderBottomWidth: 0,
    marginBottom: 0,
  },
  warningSectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.Medium,
    marginTop: Spacing.Small,
  },
  warningIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.Small,
  },
  warningSectionTitle: {
    fontWeight: '600',
    marginLeft: Spacing.Small,
  },
  warningItem: {
    flexDirection: 'row',
    marginBottom: Spacing.Medium,
    paddingLeft: Spacing.Small,
  },
  warningDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: Colors.Error,
    marginTop: 6,
    marginRight: Spacing.Small,
  },
  warningContent: {
    flex: 1,
  },
  warningIngredient: {
    fontWeight: '600',
    marginBottom: Spacing.ExtraSmall,
  },
  warningMessage: {
    lineHeight: 18,
  },
  warningCountBadge: {
    backgroundColor: Colors.Error,
    borderRadius: 9999, // Round border radius
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Spacing.Small,
  },
  warningCountText: {
    color: Colors.BackgroundPrimary,
    fontWeight: '600',
    fontSize: 12,
  },

  // Ingredient section
  ingredientTabs: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: Colors.SurfaceSecondary,
    marginBottom: Spacing.Medium,
  },
  ingredientTab: {
    paddingVertical: Spacing.Small,
    marginRight: Spacing.Large,
    borderBottomWidth: 2,
  },
  tabContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tabText: {
    marginLeft: Spacing.ExtraSmall,
    fontWeight: '500',
  },
  ingredientList: {
    paddingBottom: Spacing.Small,
  },
  ingredientItem: {
    flexDirection: 'row',
    marginBottom: Spacing.Medium,
  },
  ingredientIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.Small,
  },
  ingredientContent: {
    flex: 1,
  },
  ingredientName: {
    fontWeight: '600',
    marginBottom: Spacing.ExtraSmall,
  },
  ingredientDescription: {
    lineHeight: 18,
  },

  // Effects sections
  effectsList: {
    marginTop: Spacing.Small,
  },
  effectItem: {
    flexDirection: 'row',
    marginBottom: Spacing.Small,
    alignItems: 'flex-start',
  },
  effectBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.DarkText,
    marginTop: 8,
    marginRight: Spacing.Small,
  },
  effectText: {
    flex: 1,
    lineHeight: 22,
  },

  // Image section
  imageContainer: {
    width: '100%',
    height: 180,
    borderRadius: 12, // Medium border radius
    overflow: 'hidden',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: '#333',
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholderText: {
    textAlign: 'center',
  },

  // Fixed button at bottom
  fixedButtonContainer: {
    position: 'absolute',
    bottom: Platform.OS === 'ios' ? 34 : 20, // Account for iPhone home indicator
    left: 0,
    right: 0,
    zIndex: 1000, // Ensure it's above everything else
    paddingHorizontal: Spacing.Large,
    paddingVertical: Spacing.Medium,
  },
  fixedButton: {
    width: '100%',
    height: 56, // Taller for better appearance
    borderRadius: 28, // More rounded corners
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 15,
  },
  buttonIcon: {
    marginRight: Spacing.Small,
    width: 24,
    height: 24,
  },
  iconWrapper: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.Small,
  },
});

export default ScanResultsScreen;
